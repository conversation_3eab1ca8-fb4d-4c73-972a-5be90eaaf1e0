# 崩溃日志记录系统使用说明

## 概述

翻译软件现在配备了完善的崩溃日志记录系统，能够自动捕获和记录程序崩溃信息，帮助快速定位和修复问题。

## 功能特性

### 🔍 自动崩溃检测
- **全局异常处理器**：捕获主线程中的未处理异常
- **线程异常处理器**：捕获子线程中的未处理异常
- **关键位置监控**：在翻译、GUI、网络等关键功能中增强异常处理

### 📝 详细崩溃报告
每个崩溃报告包含：
- **崩溃时间**：精确的时间戳
- **崩溃类型**：主线程异常、线程异常、手动记录等
- **异常信息**：异常类型和详细错误信息
- **系统信息**：操作系统、Python版本、内存使用等
- **线程信息**：活跃线程数量和名称列表
- **环境变量**：相关的系统环境变量
- **完整堆栈跟踪**：详细的异常调用栈

### 🎨 用户友好的显示
- **彩色控制台输出**：使用颜色区分不同类型的信息
- **简化的错误提示**：在控制台显示关键信息
- **详细日志文件**：完整信息保存到文件

## 文件位置

### 崩溃日志文件
- **文件名**：`crash.log`
- **位置**：程序根目录（与主程序文件同级）
- **编码**：UTF-8

### 主日志文件
- **文件名**：`app.log`
- **位置**：程序根目录
- **内容**：包含崩溃的简要记录

## 使用方法

### 自动记录
程序会自动捕获以下类型的崩溃：
```python
# 主线程未捕获异常
try:
    root.mainloop()
except Exception as e:
    # 自动记录到崩溃日志
    pass

# 线程异常（Python 3.8+）
def worker_thread():
    raise RuntimeError("线程异常")
    # 自动记录到崩溃日志
```

### 手动记录
在代码中手动记录异常：
```python
from 语言互译 import log_manual_crash

try:
    # 可能出错的代码
    risky_operation()
except Exception as e:
    # 手动记录崩溃信息
    log_manual_crash("操作失败", e)
```

## 崩溃日志示例

```
================================================================================
程序崩溃报告
================================================================================
崩溃时间: 2025-05-31 10:22:27
崩溃类型: 主线程异常
异常类型: ValueError
异常信息: 翻译API返回无效数据

系统信息:
操作系统: Windows 11
Python版本: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
程序路径: C:\翻译软件\语言互译.py
是否打包: False
内存信息: 总内存: 16 GB, 可用内存: 8 GB, 内存使用率: 50.2%
活跃线程数: 3
线程列表: MainThread, ConsoleThread, KeyboardListener

异常堆栈:
Traceback (most recent call last):
  File "语言互译.py", line 3515, in translate_text_async
    result = process_api_response(response)
  File "语言互译.py", line 3600, in process_api_response
    raise ValueError("API返回无效数据")
ValueError: 翻译API返回无效数据
================================================================================
```

## 故障排除

### 常见问题

1. **崩溃日志文件未生成**
   - 检查程序目录的写入权限
   - 确保磁盘空间充足

2. **系统信息不完整**
   - 安装 `psutil` 库获取详细内存信息：`pip install psutil`

3. **线程异常未捕获**
   - 确保使用 Python 3.8+ 版本
   - 检查 `threading.excepthook` 是否可用

### 调试建议

1. **查看崩溃日志**：首先检查 `crash.log` 文件
2. **检查主日志**：查看 `app.log` 中的相关错误信息
3. **重现问题**：根据堆栈跟踪信息重现崩溃场景
4. **系统环境**：注意系统信息中的环境差异

## 开发者信息

### 添加新的崩溃监控点
```python
try:
    # 关键操作
    critical_operation()
except Exception as e:
    logger.error(f"关键操作失败: {e}", exc_info=True)
    # 记录崩溃信息
    log_manual_crash(f"关键操作失败: {str(e)}", e)
    # 恢复或清理操作
    cleanup()
```

### 自定义崩溃处理
```python
# 在CrashLogger类中添加自定义处理逻辑
def custom_crash_handler(self, crash_type, exc_type, exc_value, exc_traceback):
    # 自定义处理逻辑
    pass
```

## 注意事项

1. **隐私保护**：崩溃日志可能包含系统路径等信息，分享时请注意隐私
2. **文件大小**：崩溃日志会累积增长，建议定期清理旧日志
3. **性能影响**：崩溃记录对正常运行性能影响极小
4. **兼容性**：支持 Python 3.7+ 版本，部分功能需要更高版本

## 更新日志

- **v2.1.1**：首次引入完整的崩溃日志记录系统
- 支持主线程和子线程异常捕获
- 详细的系统信息收集
- 彩色控制台输出
- 手动崩溃记录功能
