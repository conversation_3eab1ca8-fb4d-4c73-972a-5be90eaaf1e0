2025-05-31 10:16:05,873 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 10:16:05,877 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml 不存在，正在自动生成默认配置...
2025-05-31 10:16:05,878 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:05,879 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:05,902 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:05,903 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:15,766 - INFO - API密钥验证成功
2025-05-31 10:16:15,795 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:15,796 - INFO - 已将加密的API密钥保存到配置文件
2025-05-31 10:16:15,797 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:15,812 - INFO - [INIT] 日志系统已初始化，调试模式：关闭
2025-05-31 10:16:15,874 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml 不存在，正在生成默认配置...
2025-05-31 10:16:15,876 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:15,932 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:16,060 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:16,062 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:16,063 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:16,193 - INFO - 语言模式配置已加载。
2025-05-31 10:16:16,194 - INFO - 语言模式配置已加载。
2025-05-31 10:16:16,194 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 10:16:16,195 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 10:16:16,207 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 10:16:16,314 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 10:16:16,917 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:16:16,918 - INFO - API服务正常
2025-05-31 10:16:17,133 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:16:21,230 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:21,252 - INFO - 已切换到翻译模式 2: 中文-韩文-敬语
2025-05-31 10:22:27,565 - CRITICAL - 程序崩溃: 手动记录: 测试手动崩溃记录 - ValueError: 这是一个测试异常
2025-05-31 10:22:27,566 - CRITICAL - 崩溃详情已记录到: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\crash.log
2025-05-31 10:22:27,675 - CRITICAL - 程序崩溃: 线程异常 (TestThread) - RuntimeError: 测试线程异常
2025-05-31 10:22:27,675 - CRITICAL - 崩溃详情已记录到: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\crash.log
2025-05-31 10:24:13,489 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 10:24:13,508 - INFO - [INIT] 日志系统已初始化，调试模式：关闭
2025-05-31 10:24:13,680 - INFO - 语言模式配置已加载。
2025-05-31 10:24:13,681 - INFO - 语言模式配置已加载。
2025-05-31 10:24:13,682 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 10:24:13,682 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 10:24:13,687 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 10:24:13,804 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 10:24:15,492 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:24:16,689 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:24:16,690 - INFO - API服务正常
2025-05-31 10:24:27,066 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:24:27,082 - DEBUG - 日志系统已更新：debug_mode=True, log_level=10
2025-05-31 10:24:27,082 - INFO - [CONFIG] 调试模式已开启 - 将显示详细的翻译信息
2025-05-31 10:25:15,730 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 10:25:15,750 - DEBUG - 日志系统已更新：debug_mode=True, log_level=10
2025-05-31 10:25:15,751 - INFO - [INIT] 日志系统已初始化，调试模式：开启
2025-05-31 10:25:15,925 - INFO - 语言模式配置已加载。
2025-05-31 10:25:15,926 - INFO - 语言模式配置已加载。
2025-05-31 10:25:15,927 - DEBUG - 创建LRU缓存，容量: 100
2025-05-31 10:25:15,927 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 10:25:15,927 - DEBUG - 创建LRU缓存，容量: 50
2025-05-31 10:25:15,927 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 10:25:15,931 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 10:25:15,931 - DEBUG - Translator: ServiceManager initialized
2025-05-31 10:25:15,931 - DEBUG - Using proactor: IocpProactor
2025-05-31 10:25:15,932 - DEBUG - Translator: Attempting to create main window...
2025-05-31 10:25:15,932 - DEBUG - 初始化翻译器主窗口设置
2025-05-31 10:25:15,932 - DEBUG - 主窗口设置完成
2025-05-31 10:25:15,933 - DEBUG - Translator: Main window created.
2025-05-31 10:25:15,933 - DEBUG - Translator: Attempting to setup keyboard listener...
2025-05-31 10:25:15,934 - DEBUG - 设置键盘监听器
2025-05-31 10:25:15,934 - DEBUG - Translator: Keyboard listener setup.
2025-05-31 10:25:15,935 - DEBUG - Translator: Attempting to start async loop thread...
2025-05-31 10:25:15,936 - DEBUG - Translator: Async loop thread started.
2025-05-31 10:25:15,936 - DEBUG - Translator: __init__ completed.
2025-05-31 10:25:16,034 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 10:25:16,070 - DEBUG - API加密工具已初始化
2025-05-31 10:25:16,073 - DEBUG - API密钥解密成功
2025-05-31 10:25:16,073 - DEBUG - API密钥解密成功
2025-05-31 10:25:16,074 - DEBUG - API密钥解密成功
2025-05-31 10:25:16,074 - DEBUG - API密钥解密成功
2025-05-31 10:25:16,075 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 10:25:16,077 - DEBUG - API密钥解密成功
2025-05-31 10:25:16,077 - DEBUG - API密钥解密成功
2025-05-31 10:25:16,078 - DEBUG - API密钥解密成功
2025-05-31 10:25:16,078 - DEBUG - API密钥解密成功
2025-05-31 10:25:16,079 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 10:25:17,544 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 10:25:17,646 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 10:25:17,647 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:25:18,742 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 10:25:18,849 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 10:25:18,851 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:25:18,851 - INFO - API服务正常
2025-05-31 10:26:31,565 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:26:31,565 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:26:31,569 - INFO - 【原文】
저는 일하는중입니다
2025-05-31 10:26:31,571 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 96, 3813.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:26:31,571 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.942)]
2025-05-31 10:26:31,572 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-31 10:26:31,572 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-31 10:26:31,572 - INFO - 检测到原文语言: ko
2025-05-31 10:26:31,573 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:26:31,573 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 10:26:31,573 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-31 10:26:31,574 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-31 10:26:31,574 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-31 10:26:31,574 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 10:26:31,574 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从韩文翻译成中文：
저는 일하는중입니다
2025-05-31 10:26:31,574 - DEBUG - 【构建提示词】长度: 596 字符
2025-05-31 10:26:31,575 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 10:26:31,595 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:26:31,762 - DEBUG - API密钥解密成功
2025-05-31 10:26:31,763 - DEBUG - API密钥解密成功
2025-05-31 10:26:31,764 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:26:31,764 - DEBUG - 思考预算设置为 0，思考模式已禁用
2025-05-31 10:26:31,765 - DEBUG - 已为模型 gemini-2.5-flash-preview-04-17 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:26:31,769 - INFO - 【原文】
저는 일하는중입니다
2025-05-31 10:26:31,769 - INFO - 【原文】
저는 일하는중입니다
2025-05-31 10:26:31,771 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 96, 3813.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:26:31,771 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.942)]
2025-05-31 10:26:31,771 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-31 10:26:31,771 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-31 10:26:31,771 - INFO - 检测到原文语言: ko
2025-05-31 10:26:31,771 - INFO - 检测到原文语言: ko
2025-05-31 10:26:31,771 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:26:31,771 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:26:31,771 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 10:26:31,771 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-31 10:26:31,771 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-31 10:26:31,772 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-31 10:26:31,772 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 10:26:31,772 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从韩文翻译成中文：
저는 일하는중입니다
2025-05-31 10:26:31,772 - DEBUG - 【构建提示词】长度: 596 字符
2025-05-31 10:26:31,773 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 10:26:31,777 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:26:31,778 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:26:31,778 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从韩文翻译成中文：\n저는 일하는중입니다"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:26:31,787 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:26:31,787 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:26:31,994 - DEBUG - API密钥解密成功
2025-05-31 10:26:31,994 - DEBUG - API密钥解密成功
2025-05-31 10:26:31,994 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:26:31,995 - DEBUG - 思考预算设置为 0，思考模式已禁用
2025-05-31 10:26:31,995 - DEBUG - 已为模型 gemini-2.5-flash-preview-04-17 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:26:31,995 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:26:31,996 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:26:31,996 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从韩文翻译成中文：\n저는 일하는중입니다"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:26:33,509 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 429, 响应文本 (前1000字符): {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.5-flash"
            },
            "quotaValue": "500"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ...
2025-05-31 10:26:33,510 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:26:33,511 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:26:33,512 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:26:33,512 - DEBUG - API密钥解密成功
2025-05-31 10:26:33,512 - DEBUG - API密钥解密成功
2025-05-31 10:26:33,512 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:26:33,512 - DEBUG - 模型 gemini-2.0-flash-lite-preview-02-05 不支持思考模式，将禁用思考配置
2025-05-31 10:26:33,513 - DEBUG - 已为模型 gemini-2.0-flash-lite-preview-02-05 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:26:33,513 - DEBUG - 【API请求JSON】模型: gemini-2.0-flash-lite-preview-02-05, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-preview-02-05:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:26:33,514 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:26:33,514 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从韩文翻译成中文：\n저는 일하는중입니다"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:26:33,620 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:26:33,621 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:26:33,621 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:26:33,645 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 429, 响应文本 (前1000字符): {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.5-flash",
              "location": "global"
            },
            "quotaValue": "500"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ...
2025-05-31 10:26:33,646 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:26:33,646 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:26:33,646 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:26:33,646 - DEBUG - API密钥解密成功
2025-05-31 10:26:33,646 - DEBUG - API密钥解密成功
2025-05-31 10:26:33,646 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:26:33,646 - DEBUG - 模型 gemini-2.0-flash-lite-preview-02-05 不支持思考模式，将禁用思考配置
2025-05-31 10:26:33,646 - DEBUG - 已为模型 gemini-2.0-flash-lite-preview-02-05 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:26:33,647 - DEBUG - 【API请求JSON】模型: gemini-2.0-flash-lite-preview-02-05, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-preview-02-05:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:26:33,647 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:26:33,648 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从韩文翻译成中文：\n저는 일하는중입니다"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:26:35,455 - DEBUG - 【API响应原始文本】模型: gemini-2.0-flash-lite-preview-02-05, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "我正在工作。\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "safetyRatings": [
        {
          "category": "HARM_CATEGORY_HATE_SPEECH",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HARASSMENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          "probability": "NEGLIGIBLE"
        }
      ],
      "avgLogprobs": -0.076138097047805789
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 391,
    "candidatesTokenCount": 5,
    "totalTokenCount": 396,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 391
      }
    ],
    "candidatesTokensDetails": [
      {
        "moda...
2025-05-31 10:26:35,456 - DEBUG - 【API响应原始文本】模型: gemini-2.0-flash-lite-preview-02-05, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "我正在工作。\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "safetyRatings": [
        {
          "category": "HARM_CATEGORY_HATE_SPEECH",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HARASSMENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          "probability": "NEGLIGIBLE"
        }
      ],
      "avgLogprobs": -0.057448822259902957
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 391,
    "candidatesTokenCount": 5,
    "totalTokenCount": 396,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 391
      }
    ],
    "candidatesTokensDetails": [
      {
        "moda...
2025-05-31 10:26:35,456 - DEBUG - 【API响应JSON对象】模型: gemini-2.0-flash-lite-preview-02-05, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '我正在工作。\n'}], 'role': 'model'}, 'finishReason': 'STOP', 'safetyRatings': [{'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE'}], 'avgLogprobs': -0.05744882225990296}], 'usageMetadata': {'promptTokenCount': 391, 'candidatesTokenCount': 5, 'totalTokenCount': 396, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 391}], 'candidatesTokensDetails': [{'modality': 'TEXT', 'tokenCount': 5}]}, 'modelVersion': 'gemini-2.0-flash-lite-preview-02-05', 'responseId': 'y1o6aOH-H7So1dkPwdyIiA0'}
2025-05-31 10:26:35,456 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-31 10:26:35,456 - DEBUG - 【API响应JSON对象】模型: gemini-2.0-flash-lite-preview-02-05, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '我正在工作。\n'}], 'role': 'model'}, 'finishReason': 'STOP', 'safetyRatings': [{'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE'}], 'avgLogprobs': -0.07613809704780579}], 'usageMetadata': {'promptTokenCount': 391, 'candidatesTokenCount': 5, 'totalTokenCount': 396, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 391}], 'candidatesTokensDetails': [{'modality': 'TEXT', 'tokenCount': 5}]}, 'modelVersion': 'gemini-2.0-flash-lite-preview-02-05', 'responseId': 'y1o6aObWFrSo1dkPwdyIiA0'}
2025-05-31 10:26:35,457 - DEBUG -   - 思考Token数: 0
2025-05-31 10:26:35,457 - DEBUG -   - 提示Token数: 391
2025-05-31 10:26:35,457 - DEBUG -   - 输出Token数: 5
2025-05-31 10:26:35,457 - DEBUG -   - 总Token数: 396
2025-05-31 10:26:35,457 - DEBUG - 【Token使用情况】模型: gemini-2.0-flash-lite-preview-02-05
2025-05-31 10:26:35,458 - DEBUG -   - 思考Token数: 0
2025-05-31 10:26:35,458 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:26:35,458 - DEBUG - 基于特征补充候选: zh (score: 0.4167)
2025-05-31 10:26:35,458 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.417)]
2025-05-31 10:26:35,458 - DEBUG -   - 提示Token数: 391
2025-05-31 10:26:35,458 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 10:26:35,458 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 10:26:35,458 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-31 10:26:35,458 - DEBUG -   - 输出Token数: 5
2025-05-31 10:26:35,459 - DEBUG -   - 总Token数: 396
2025-05-31 10:26:35,459 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:26:35,460 - DEBUG - 基于特征补充候选: zh (score: 0.4167)
2025-05-31 10:26:35,460 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.417)]
2025-05-31 10:26:35,460 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 10:26:35,460 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 10:26:35,461 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-31 10:26:35,926 - DEBUG - 输入框内容已替换为: 我正在工作。
2025-05-31 10:26:35,926 - DEBUG - 输入框内容已替换为: 我正在工作。
2025-05-31 10:26:35,939 - INFO - 【翻译结果】
我正在工作。
2025-05-31 10:26:35,939 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:26:35,938 - INFO - 【翻译结果】
我正在工作。
2025-05-31 10:26:35,939 - INFO - 【翻译结果】
我正在工作。
2025-05-31 10:26:35,939 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:26:35,939 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:26:35,940 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-31 10:26:35,940 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-31 10:26:35,940 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-31 10:26:35,940 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-31 10:26:35,946 - INFO - 已立即保存 2 条缓存记录
2025-05-31 10:26:35,949 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-31 10:26:35,950 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:26:35,950 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:26:35,951 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:26:35,962 - INFO - 已立即保存 2 条缓存记录
2025-05-31 10:26:35,966 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-31 10:27:47,526 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:27:47,526 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:27:47,526 - INFO - 【原文】
啊。辛苦了，你工作能使用手机么？
2025-05-31 10:27:47,526 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:27:47,527 - INFO - 【原文】
啊。辛苦了，你工作能使用手机么？
2025-05-31 10:27:47,527 - INFO - 检测到原文语言: zh
2025-05-31 10:27:47,527 - INFO - 执行正向翻译为: ko
2025-05-31 10:27:47,527 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-31 10:27:47,528 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1779.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:27:47,528 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.923)]
2025-05-31 10:27:47,528 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 10:27:47,528 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 10:27:47,528 - INFO - 检测到原文语言: zh
2025-05-31 10:27:47,529 - INFO - 执行正向翻译为: ko
2025-05-31 10:27:47,529 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 10:27:47,529 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-31 10:27:47,528 - INFO - 【原文】
啊。辛苦了，你工作能使用手机么？
2025-05-31 10:27:47,529 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-31 10:27:47,529 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 10:27:47,529 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-31 10:27:47,529 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 저는 일하는중입니다
翻译: 我正在工作。

将以下内容从中文翻译成韩文，使用敬语：
啊。辛苦了，你工作能使用手机么？
2025-05-31 10:27:47,529 - DEBUG - 【构建提示词】长度: 665 字符
2025-05-31 10:27:47,530 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 10:27:47,529 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1779.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:27:47,530 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.923)]
2025-05-31 10:27:47,531 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 10:27:47,531 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 10:27:47,532 - INFO - 检测到原文语言: zh
2025-05-31 10:27:47,532 - INFO - 执行正向翻译为: ko
2025-05-31 10:27:47,533 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 10:27:47,533 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-31 10:27:47,534 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-31 10:27:47,534 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 10:27:47,535 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-31 10:27:47,535 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 저는 일하는중입니다
翻译: 我正在工作。

将以下内容从中文翻译成韩文，使用敬语：
啊。辛苦了，你工作能使用手机么？
2025-05-31 10:27:47,535 - DEBUG - 【构建提示词】长度: 665 字符
2025-05-31 10:27:47,536 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 10:27:47,536 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:27:47,539 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:27:47,545 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:27:47,725 - DEBUG - API密钥解密成功
2025-05-31 10:27:47,725 - DEBUG - API密钥解密成功
2025-05-31 10:27:47,725 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:27:47,726 - DEBUG - 思考预算设置为 0，思考模式已禁用
2025-05-31 10:27:47,726 - DEBUG - 已为模型 gemini-2.5-flash-preview-04-17 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:27:47,726 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:27:47,727 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:27:47,727 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语\n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 저는 일하는중입니다\n翻译: 我正在工作。\n\n将以下内容从中文翻译成韩文，使用敬语：\n啊。辛苦了，你工作能使用手机么？"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:27:47,738 - DEBUG - API密钥解密成功
2025-05-31 10:27:47,739 - DEBUG - API密钥解密成功
2025-05-31 10:27:47,740 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:27:47,740 - DEBUG - 思考预算设置为 0，思考模式已禁用
2025-05-31 10:27:47,740 - DEBUG - 已为模型 gemini-2.5-flash-preview-04-17 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:27:47,741 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:27:47,742 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:27:47,742 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语\n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 저는 일하는중입니다\n翻译: 我正在工作。\n\n将以下内容从中文翻译成韩文，使用敬语：\n啊。辛苦了，你工作能使用手机么？"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:27:48,333 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 429, 响应文本 (前1000字符): {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.5-flash",
              "location": "global"
            },
            "quotaValue": "500"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ...
2025-05-31 10:27:48,334 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:27:48,334 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:27:48,334 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:27:48,334 - DEBUG - API密钥解密成功
2025-05-31 10:27:48,334 - DEBUG - API密钥解密成功
2025-05-31 10:27:48,334 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:27:48,334 - DEBUG - 模型 gemini-2.0-flash-lite-preview-02-05 不支持思考模式，将禁用思考配置
2025-05-31 10:27:48,334 - DEBUG - 已为模型 gemini-2.0-flash-lite-preview-02-05 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:27:48,335 - DEBUG - 【API请求JSON】模型: gemini-2.0-flash-lite-preview-02-05, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-preview-02-05:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:27:48,336 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:27:48,336 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语\n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 저는 일하는중입니다\n翻译: 我正在工作。\n\n将以下内容从中文翻译成韩文，使用敬语：\n啊。辛苦了，你工作能使用手机么？"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:27:49,294 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:27:49,294 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:27:49,294 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:27:49,294 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 429, 响应文本 (前1000字符): {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.5-flash"
            },
            "quotaValue": "500"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ...
2025-05-31 10:27:49,294 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:27:49,295 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:27:49,296 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:27:49,296 - DEBUG - API密钥解密成功
2025-05-31 10:27:49,297 - DEBUG - API密钥解密成功
2025-05-31 10:27:49,297 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:27:49,297 - DEBUG - 模型 gemini-2.0-flash-lite-preview-02-05 不支持思考模式，将禁用思考配置
2025-05-31 10:27:49,297 - DEBUG - 已为模型 gemini-2.0-flash-lite-preview-02-05 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:27:49,298 - DEBUG - 【API请求JSON】模型: gemini-2.0-flash-lite-preview-02-05, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-preview-02-05:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:27:49,299 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:27:49,299 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语\n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 저는 일하는중입니다\n翻译: 我正在工作。\n\n将以下内容从中文翻译成韩文，使用敬语：\n啊。辛苦了，你工作能使用手机么？"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:27:50,146 - DEBUG - 【API响应原始文本】模型: gemini-2.0-flash-lite-preview-02-05, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "safetyRatings": [
        {
          "category": "HARM_CATEGORY_HATE_SPEECH",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HARASSMENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          "probability": "NEGLIGIBLE"
        }
      ],
      "avgLogprobs": -0.10905331915075128
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 438,
    "candidatesTokenCount": 22,
    "totalTokenCount": 460,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 438
      }
    ],
    "candidatesTokensDetails...
2025-05-31 10:27:50,146 - DEBUG - 【API响应JSON对象】模型: gemini-2.0-flash-lite-preview-02-05, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?\n'}], 'role': 'model'}, 'finishReason': 'STOP', 'safetyRatings': [{'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE'}], 'avgLogprobs': -0.10905331915075128}], 'usageMetadata': {'promptTokenCount': 438, 'candidatesTokenCount': 22, 'totalTokenCount': 460, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 438}], 'candidatesTokensDetails': [{'modality': 'TEXT', 'tokenCount': 22}]}, 'modelVersion': 'gemini-2.0-flash-lite-preview-02-05', 'responseId': 'Fls6aNyMCfC-gLUPq9uj-Qg'}
2025-05-31 10:27:50,146 - DEBUG - 【Token使用情况】模型: gemini-2.0-flash-lite-preview-02-05
2025-05-31 10:27:50,146 - DEBUG -   - 思考Token数: 0
2025-05-31 10:27:50,146 - DEBUG -   - 提示Token数: 438
2025-05-31 10:27:50,146 - DEBUG -   - 输出Token数: 22
2025-05-31 10:27:50,147 - DEBUG -   - 总Token数: 460
2025-05-31 10:27:50,147 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3718.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:27:50,147 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.902)]
2025-05-31 10:27:50,147 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-31 10:27:50,147 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-31 10:27:50,148 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-31 10:27:50,607 - DEBUG - 输入框内容已替换为: 아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?
2025-05-31 10:27:50,609 - INFO - 【翻译结果】
아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?
2025-05-31 10:27:50,609 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:27:50,610 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-31 10:27:50,610 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-31 10:27:50,612 - INFO - 已立即保存 2 条缓存记录
2025-05-31 10:27:50,612 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:27:50,614 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-31 10:27:51,131 - DEBUG - 【API响应原始文本】模型: gemini-2.0-flash-lite-preview-02-05, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "safetyRatings": [
        {
          "category": "HARM_CATEGORY_HATE_SPEECH",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HARASSMENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          "probability": "NEGLIGIBLE"
        }
      ],
      "avgLogprobs": -0.09198535572398793
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 438,
    "candidatesTokenCount": 22,
    "totalTokenCount": 460,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 438
      }
    ],
    "candidatesTokensDetails...
2025-05-31 10:27:51,132 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-31 10:27:51,132 - DEBUG - 【API响应JSON对象】模型: gemini-2.0-flash-lite-preview-02-05, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?\n'}], 'role': 'model'}, 'finishReason': 'STOP', 'safetyRatings': [{'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE'}], 'avgLogprobs': -0.09198535572398793}], 'usageMetadata': {'promptTokenCount': 438, 'candidatesTokenCount': 22, 'totalTokenCount': 460, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 438}], 'candidatesTokensDetails': [{'modality': 'TEXT', 'tokenCount': 22}]}, 'modelVersion': 'gemini-2.0-flash-lite-preview-02-05', 'responseId': 'F1s6aIG5B8qF1dkPq8eZ2As'}
2025-05-31 10:27:51,133 - DEBUG - 【Token使用情况】模型: gemini-2.0-flash-lite-preview-02-05
2025-05-31 10:27:51,133 - DEBUG -   - 思考Token数: 0
2025-05-31 10:27:51,134 - DEBUG -   - 提示Token数: 438
2025-05-31 10:27:51,134 - DEBUG -   - 输出Token数: 22
2025-05-31 10:27:51,134 - DEBUG -   - 总Token数: 460
2025-05-31 10:27:51,135 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3718.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:27:51,136 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.902)]
2025-05-31 10:27:51,136 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-31 10:27:51,136 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-31 10:27:51,137 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-31 10:27:51,590 - INFO - 【翻译结果】
아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?
2025-05-31 10:27:51,590 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:27:51,592 - DEBUG - 输入框内容已替换为: 아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?
2025-05-31 10:27:51,594 - INFO - 【翻译结果】
아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?
2025-05-31 10:27:51,595 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:27:51,595 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-31 10:27:51,595 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:27:51,595 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-31 10:27:51,597 - INFO - 已立即保存 2 条缓存记录
2025-05-31 10:27:51,598 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:27:51,600 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-31 10:28:02,805 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:28:02,805 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:28:02,805 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:28:02,806 - INFO - 【原文】
광안리는 이미 지나왔어요...
2025-05-31 10:28:02,806 - INFO - 【原文】
광안리는 이미 지나왔어요...
2025-05-31 10:28:02,807 - INFO - 检测到原文语言: ko
2025-05-31 10:28:02,807 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:28:02,807 - INFO - 模式 2 当前上下文数量: 2（最大: 8）
2025-05-31 10:28:02,807 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3754.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:28:02,808 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.885)]
2025-05-31 10:28:02,809 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-31 10:28:02,809 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-31 10:28:02,809 - INFO - 检测到原文语言: ko
2025-05-31 10:28:02,809 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:28:02,810 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 10:28:02,810 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-31 10:28:02,810 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-31 10:28:02,810 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-31 10:28:02,811 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 10:28:02,811 - INFO - 模式 2 当前上下文数量: 2（最大: 8）
2025-05-31 10:28:02,811 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 저는 일하는중입니다
翻译: 我正在工作。
原文: 啊。辛苦了，你工作能使用手机么？
翻译: 아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?

将以下内容从韩文翻译成中文：
광안리는 이미 지나왔어요...
2025-05-31 10:28:02,812 - DEBUG - 【构建提示词】长度: 713 字符
2025-05-31 10:28:02,812 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 10:28:02,813 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:28:02,820 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:28:02,929 - DEBUG - API密钥解密成功
2025-05-31 10:28:02,930 - DEBUG - API密钥解密成功
2025-05-31 10:28:02,930 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:28:02,930 - DEBUG - 思考预算设置为 0，思考模式已禁用
2025-05-31 10:28:02,931 - DEBUG - 已为模型 gemini-2.5-flash-preview-04-17 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:28:02,932 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:28:02,932 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:28:02,932 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 저는 일하는중입니다\n翻译: 我正在工作。\n原文: 啊。辛苦了，你工作能使用手机么？\n翻译: 아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?\n\n将以下内容从韩文翻译成中文：\n광안리는 이미 지나왔어요..."
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:28:03,007 - INFO - 【原文】
광안리는 이미 지나왔어요...
2025-05-31 10:28:03,008 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3754.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:28:03,008 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.885)]
2025-05-31 10:28:03,008 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-31 10:28:03,008 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-31 10:28:03,008 - INFO - 检测到原文语言: ko
2025-05-31 10:28:03,008 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:28:03,008 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 10:28:03,008 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-31 10:28:03,009 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-31 10:28:03,009 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-31 10:28:03,009 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 10:28:03,009 - INFO - 模式 2 当前上下文数量: 2（最大: 8）
2025-05-31 10:28:03,009 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 저는 일하는중입니다
翻译: 我正在工作。
原文: 啊。辛苦了，你工作能使用手机么？
翻译: 아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?

将以下内容从韩文翻译成中文：
광안리는 이미 지나왔어요...
2025-05-31 10:28:03,009 - DEBUG - 【构建提示词】长度: 713 字符
2025-05-31 10:28:03,009 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 10:28:03,012 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:28:03,116 - DEBUG - API密钥解密成功
2025-05-31 10:28:03,116 - DEBUG - API密钥解密成功
2025-05-31 10:28:03,117 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:28:03,117 - DEBUG - 思考预算设置为 0，思考模式已禁用
2025-05-31 10:28:03,117 - DEBUG - 已为模型 gemini-2.5-flash-preview-04-17 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:28:03,119 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:28:03,119 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:28:03,119 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 저는 일하는중입니다\n翻译: 我正在工作。\n原文: 啊。辛苦了，你工作能使用手机么？\n翻译: 아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?\n\n将以下内容从韩文翻译成中文：\n광안리는 이미 지나왔어요..."
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:28:03,644 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 429, 响应文本 (前1000字符): {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.5-flash"
            },
            "quotaValue": "500"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ...
2025-05-31 10:28:03,644 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:28:03,644 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:28:03,644 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:28:03,644 - DEBUG - API密钥解密成功
2025-05-31 10:28:03,645 - DEBUG - API密钥解密成功
2025-05-31 10:28:03,645 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:28:03,645 - DEBUG - 模型 gemini-2.0-flash-lite-preview-02-05 不支持思考模式，将禁用思考配置
2025-05-31 10:28:03,645 - DEBUG - 已为模型 gemini-2.0-flash-lite-preview-02-05 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:28:03,645 - DEBUG - 【API请求JSON】模型: gemini-2.0-flash-lite-preview-02-05, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-preview-02-05:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:28:03,646 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:28:03,646 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 저는 일하는중입니다\n翻译: 我正在工作。\n原文: 啊。辛苦了，你工作能使用手机么？\n翻译: 아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?\n\n将以下内容从韩文翻译成中文：\n광안리는 이미 지나왔어요..."
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:28:04,394 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:28:04,394 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:28:04,394 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:28:04,432 - DEBUG - 【API响应原始文本】模型: gemini-2.0-flash-lite-preview-02-05, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "我已经路过广安里了……\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "safetyRatings": [
        {
          "category": "HARM_CATEGORY_HATE_SPEECH",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HARASSMENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          "probability": "NEGLIGIBLE"
        }
      ],
      "avgLogprobs": -0.23158513175116646
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 465,
    "candidatesTokenCount": 9,
    "totalTokenCount": 474,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 465
      }
    ],
    "candidatesTokensDetails": [
      {
        "...
2025-05-31 10:28:04,432 - DEBUG - 【API响应JSON对象】模型: gemini-2.0-flash-lite-preview-02-05, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '我已经路过广安里了……\n'}], 'role': 'model'}, 'finishReason': 'STOP', 'safetyRatings': [{'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE'}], 'avgLogprobs': -0.23158513175116646}], 'usageMetadata': {'promptTokenCount': 465, 'candidatesTokenCount': 9, 'totalTokenCount': 474, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 465}], 'candidatesTokensDetails': [{'modality': 'TEXT', 'tokenCount': 9}]}, 'modelVersion': 'gemini-2.0-flash-lite-preview-02-05', 'responseId': 'JFs6aJf4HYOp1dkPmffv0Aw'}
2025-05-31 10:28:04,432 - DEBUG - 【Token使用情况】模型: gemini-2.0-flash-lite-preview-02-05
2025-05-31 10:28:04,432 - DEBUG -   - 思考Token数: 0
2025-05-31 10:28:04,432 - DEBUG -   - 提示Token数: 465
2025-05-31 10:28:04,433 - DEBUG -   - 输出Token数: 9
2025-05-31 10:28:04,433 - DEBUG -   - 总Token数: 474
2025-05-31 10:28:04,433 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 96, 2157.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:28:04,433 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.917)]
2025-05-31 10:28:04,433 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 10:28:04,434 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 10:28:04,434 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-31 10:28:04,443 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 429, 响应文本 (前1000字符): {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.5-flash"
            },
            "quotaValue": "500"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ...
2025-05-31 10:28:04,444 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:28:04,444 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:28:04,445 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:28:04,445 - DEBUG - API密钥解密成功
2025-05-31 10:28:04,445 - DEBUG - API密钥解密成功
2025-05-31 10:28:04,445 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:28:04,446 - DEBUG - 模型 gemini-2.0-flash-lite-preview-02-05 不支持思考模式，将禁用思考配置
2025-05-31 10:28:04,446 - DEBUG - 已为模型 gemini-2.0-flash-lite-preview-02-05 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:28:04,446 - DEBUG - 【API请求JSON】模型: gemini-2.0-flash-lite-preview-02-05, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-preview-02-05:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:28:04,447 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:28:04,447 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 저는 일하는중입니다\n翻译: 我正在工作。\n原文: 啊。辛苦了，你工作能使用手机么？\n翻译: 아, 수고하셨습니다. 일하실 때 휴대폰 사용 가능하십니까?\n\n将以下内容从韩文翻译成中文：\n광안리는 이미 지나왔어요..."
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:28:04,891 - DEBUG - 输入框内容已替换为: 我已经路过广安里了……
2025-05-31 10:28:04,893 - INFO - 【翻译结果】
我已经路过广安里了……
2025-05-31 10:28:04,893 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:28:04,893 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-31 10:28:04,893 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-31 10:28:04,895 - INFO - 已立即保存 2 条缓存记录
2025-05-31 10:28:04,897 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-31 10:28:04,898 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:28:06,263 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-31 10:28:06,284 - DEBUG - 【API响应原始文本】模型: gemini-2.0-flash-lite-preview-02-05, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "我已经路过广安里了……\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "safetyRatings": [
        {
          "category": "HARM_CATEGORY_HATE_SPEECH",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HARASSMENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          "probability": "NEGLIGIBLE"
        }
      ],
      "avgLogprobs": -0.23158513175116646
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 465,
    "candidatesTokenCount": 9,
    "totalTokenCount": 474,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 465
      }
    ],
    "candidatesTokensDetails": [
      {
        "...
2025-05-31 10:28:06,284 - DEBUG - 【API响应JSON对象】模型: gemini-2.0-flash-lite-preview-02-05, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '我已经路过广安里了……\n'}], 'role': 'model'}, 'finishReason': 'STOP', 'safetyRatings': [{'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE'}], 'avgLogprobs': -0.23158513175116646}], 'usageMetadata': {'promptTokenCount': 465, 'candidatesTokenCount': 9, 'totalTokenCount': 474, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 465}], 'candidatesTokensDetails': [{'modality': 'TEXT', 'tokenCount': 9}]}, 'modelVersion': 'gemini-2.0-flash-lite-preview-02-05', 'responseId': 'Jls6aMv9E4Op1dkPmffv0Aw'}
2025-05-31 10:28:06,285 - DEBUG - 【Token使用情况】模型: gemini-2.0-flash-lite-preview-02-05
2025-05-31 10:28:06,285 - DEBUG -   - 思考Token数: 0
2025-05-31 10:28:06,285 - DEBUG -   - 提示Token数: 465
2025-05-31 10:28:06,285 - DEBUG -   - 输出Token数: 9
2025-05-31 10:28:06,286 - DEBUG -   - 总Token数: 474
2025-05-31 10:28:06,287 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 96, 2157.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:28:06,288 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.917)]
2025-05-31 10:28:06,288 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 10:28:06,288 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 10:28:06,289 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-31 10:28:06,724 - INFO - 【翻译结果】
已经路过广安里了……
2025-05-31 10:28:06,724 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:28:06,727 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:28:06,744 - DEBUG - 输入框内容已替换为: 我已经路过广安里了……
2025-05-31 10:28:06,746 - INFO - 【翻译结果】
我已经路过广安里了……
2025-05-31 10:28:06,746 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:28:06,747 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-31 10:28:06,747 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-31 10:28:06,748 - INFO - 已立即保存 2 条缓存记录
2025-05-31 10:28:06,748 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:28:06,752 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-31 10:28:44,142 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 10:28:44,163 - DEBUG - 日志系统已更新：debug_mode=True, log_level=10
2025-05-31 10:28:44,163 - INFO - [INIT] 日志系统已初始化，调试模式：开启
2025-05-31 10:28:44,347 - INFO - 语言模式配置已加载。
2025-05-31 10:28:44,348 - INFO - 语言模式配置已加载。
2025-05-31 10:28:44,349 - DEBUG - 创建LRU缓存，容量: 100
2025-05-31 10:28:44,349 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 10:28:44,349 - DEBUG - 创建LRU缓存，容量: 50
2025-05-31 10:28:44,349 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 10:28:44,354 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 10:28:44,354 - DEBUG - Translator: ServiceManager initialized
2025-05-31 10:28:44,354 - DEBUG - Using proactor: IocpProactor
2025-05-31 10:28:44,355 - DEBUG - Translator: Attempting to create main window...
2025-05-31 10:28:44,355 - DEBUG - 初始化翻译器主窗口设置
2025-05-31 10:28:44,355 - DEBUG - 主窗口设置完成
2025-05-31 10:28:44,355 - DEBUG - Translator: Main window created.
2025-05-31 10:28:44,356 - DEBUG - Translator: Attempting to setup keyboard listener...
2025-05-31 10:28:44,356 - DEBUG - 设置键盘监听器
2025-05-31 10:28:44,356 - DEBUG - Translator: Keyboard listener setup.
2025-05-31 10:28:44,356 - DEBUG - Translator: Attempting to start async loop thread...
2025-05-31 10:28:44,357 - DEBUG - Translator: Async loop thread started.
2025-05-31 10:28:44,357 - DEBUG - Translator: __init__ completed.
2025-05-31 10:28:44,447 - DEBUG - API加密工具已初始化
2025-05-31 10:28:44,448 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 10:28:44,449 - DEBUG - API密钥解密成功
2025-05-31 10:28:44,450 - DEBUG - API密钥解密成功
2025-05-31 10:28:44,451 - DEBUG - API密钥解密成功
2025-05-31 10:28:44,452 - DEBUG - API密钥解密成功
2025-05-31 10:28:44,452 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 10:28:44,455 - DEBUG - API密钥解密成功
2025-05-31 10:28:44,455 - DEBUG - API密钥解密成功
2025-05-31 10:28:44,456 - DEBUG - API密钥解密成功
2025-05-31 10:28:44,457 - DEBUG - API密钥解密成功
2025-05-31 10:28:44,457 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 10:28:44,924 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 10:28:45,032 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 10:28:45,033 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:28:46,159 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 10:28:46,266 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 10:28:46,267 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:28:46,267 - INFO - API服务正常
2025-05-31 10:29:01,982 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:29:01,985 - INFO - 【原文】
광안리는 이미 지나왔어요...
2025-05-31 10:29:01,988 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3754.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:29:01,988 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.885)]
2025-05-31 10:29:01,988 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-31 10:29:01,988 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-31 10:29:01,989 - INFO - 检测到原文语言: ko
2025-05-31 10:29:01,989 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:29:01,989 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 10:29:01,990 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-31 10:29:01,990 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-31 10:29:01,990 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-31 10:29:01,990 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 10:29:01,990 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从韩文翻译成中文：
광안리는 이미 지나왔어요...
2025-05-31 10:29:01,991 - DEBUG - 【构建提示词】长度: 602 字符
2025-05-31 10:29:01,991 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 10:29:01,998 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:29:02,104 - DEBUG - API密钥解密成功
2025-05-31 10:29:02,105 - DEBUG - API密钥解密成功
2025-05-31 10:29:02,105 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:29:02,106 - DEBUG - 思考预算设置为 0，思考模式已禁用
2025-05-31 10:29:02,106 - DEBUG - 已为模型 gemini-2.5-flash-preview-04-17 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:29:02,107 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:29:02,107 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:29:02,107 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从韩文翻译成中文：\n광안리는 이미 지나왔어요..."
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:29:03,853 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 429, 响应文本 (前1000字符): {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.5-flash"
            },
            "quotaValue": "500"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ...
2025-05-31 10:29:03,854 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:29:03,855 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:29:03,855 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:29:03,855 - DEBUG - API密钥解密成功
2025-05-31 10:29:03,856 - DEBUG - API密钥解密成功
2025-05-31 10:29:03,856 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 10:29:03,856 - DEBUG - 模型 gemini-2.0-flash-lite-preview-02-05 不支持思考模式，将禁用思考配置
2025-05-31 10:29:03,857 - DEBUG - 已为模型 gemini-2.0-flash-lite-preview-02-05 禁用思考模式(不添加thinkingConfig字段)
2025-05-31 10:29:03,857 - DEBUG - 【API请求JSON】模型: gemini-2.0-flash-lite-preview-02-05, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-preview-02-05:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 10:29:03,857 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 10:29:03,858 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从韩文翻译成中文：\n광안리는 이미 지나왔어요..."
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 10:29:05,828 - DEBUG - 【API响应原始文本】模型: gemini-2.0-flash-lite-preview-02-05, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "光安里已经过去了……\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "safetyRatings": [
        {
          "category": "HARM_CATEGORY_HATE_SPEECH",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HARASSMENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          "probability": "NEGLIGIBLE"
        }
      ],
      "avgLogprobs": -0.28401805673326763
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 395,
    "candidatesTokenCount": 7,
    "totalTokenCount": 402,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 395
      }
    ],
    "candidatesTokensDetails": [
      {
        "m...
2025-05-31 10:29:05,829 - DEBUG - 【API响应JSON对象】模型: gemini-2.0-flash-lite-preview-02-05, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '光安里已经过去了……\n'}], 'role': 'model'}, 'finishReason': 'STOP', 'safetyRatings': [{'category': 'HARM_CATEGORY_HATE_SPEECH', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_HARASSMENT', 'probability': 'NEGLIGIBLE'}, {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'probability': 'NEGLIGIBLE'}], 'avgLogprobs': -0.28401805673326763}], 'usageMetadata': {'promptTokenCount': 395, 'candidatesTokenCount': 7, 'totalTokenCount': 402, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 395}], 'candidatesTokensDetails': [{'modality': 'TEXT', 'tokenCount': 7}]}, 'modelVersion': 'gemini-2.0-flash-lite-preview-02-05', 'responseId': 'YVs6aPWaL7So1dkPwdyIiA0'}
2025-05-31 10:29:05,831 - DEBUG - 【Token使用情况】模型: gemini-2.0-flash-lite-preview-02-05
2025-05-31 10:29:05,831 - DEBUG -   - 思考Token数: 0
2025-05-31 10:29:05,831 - DEBUG -   - 提示Token数: 395
2025-05-31 10:29:05,832 - DEBUG -   - 输出Token数: 7
2025-05-31 10:29:05,832 - DEBUG -   - 总Token数: 402
2025-05-31 10:29:05,833 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 96, 2048.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:29:05,833 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.912)]
2025-05-31 10:29:05,833 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 10:29:05,833 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 10:29:05,833 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-31 10:29:06,289 - DEBUG - 输入框内容已替换为: 光安里已经过去了……
2025-05-31 10:29:06,292 - INFO - 【翻译结果】
光安里已经过去了……
2025-05-31 10:29:06,293 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:29:06,293 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-31 10:29:06,294 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-31 10:29:06,295 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:29:06,295 - INFO - 已立即保存 2 条缓存记录
2025-05-31 10:29:06,299 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-31 10:29:20,229 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:29:20,232 - INFO - 【原文】
光安里已经过去了……
2025-05-31 10:29:20,233 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 96, 2048.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 10:29:20,233 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.912)]
2025-05-31 10:29:20,234 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 10:29:20,234 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 10:29:20,234 - INFO - 检测到原文语言: zh
2025-05-31 10:29:20,234 - INFO - 执行正向翻译为: ko
2025-05-31 10:29:20,234 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 10:29:20,235 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-31 10:29:20,235 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-31 10:29:20,235 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 10:29:20,235 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-31 10:29:20,236 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 광안리는 이미 지나왔어요...
翻译: 光安里已经过去了……

将以下内容从中文翻译成韩文，使用敬语：
光安里已经过去了……
2025-05-31 10:29:20,236 - DEBUG - 【构建提示词】长度: 669 字符
2025-05-31 10:29:20,236 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 10:29:20,239 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:29:20,492 - INFO - 内存缓存命中
2025-05-31 10:29:20,493 - INFO - 【本地缓存命中】翻译结果从本地数据库获取
2025-05-31 10:29:20,949 - DEBUG - 输入框内容已替换为: 광안리는 이미 지나왔어요...
2025-05-31 10:29:20,950 - DEBUG - 缓存翻译结果处理完成：已替换输入框内容并隐藏GUI进度条
2025-05-31 10:29:20,951 - INFO - 【翻译结果】
광안리는 이미 지나왔어요...
2025-05-31 10:29:20,952 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:29:20,954 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:29:48,706 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:29:48,721 - DEBUG - 配置验证成功
2025-05-31 10:29:48,722 - DEBUG - 配置保存并验证成功
2025-05-31 10:29:48,723 - INFO - 本地缓存已关闭
2025-05-31 10:29:50,815 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:29:50,830 - DEBUG - 配置验证成功
2025-05-31 10:29:50,831 - DEBUG - 配置保存并验证成功
2025-05-31 10:29:50,832 - INFO - 本地缓存已开启
2025-05-31 10:29:54,009 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:29:54,037 - DEBUG - 配置验证成功
2025-05-31 10:29:54,038 - DEBUG - 配置保存并验证成功
2025-05-31 10:29:54,038 - INFO - 缓存优先级已设置为: 优先使用大模型
2025-05-31 10:29:55,561 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:29:55,576 - DEBUG - 配置验证成功
2025-05-31 10:29:55,577 - DEBUG - 配置保存并验证成功
2025-05-31 10:29:55,577 - INFO - 缓存优先级已设置为: 优先使用缓存
2025-05-31 10:30:39,182 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 10:30:39,204 - DEBUG - 日志系统已更新：debug_mode=True, log_level=10
2025-05-31 10:30:39,205 - INFO - [INIT] 日志系统已初始化，调试模式：开启
2025-05-31 10:30:39,396 - INFO - 语言模式配置已加载。
2025-05-31 10:30:39,397 - INFO - 语言模式配置已加载。
2025-05-31 10:30:39,397 - DEBUG - 创建LRU缓存，容量: 100
2025-05-31 10:30:39,398 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 10:30:39,398 - DEBUG - 创建LRU缓存，容量: 50
2025-05-31 10:30:39,398 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 10:30:39,404 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 10:30:39,404 - DEBUG - Translator: ServiceManager initialized
2025-05-31 10:30:39,404 - DEBUG - Using proactor: IocpProactor
2025-05-31 10:30:39,405 - DEBUG - Translator: Attempting to create main window...
2025-05-31 10:30:39,405 - DEBUG - 初始化翻译器主窗口设置
2025-05-31 10:30:39,405 - DEBUG - 主窗口设置完成
2025-05-31 10:30:39,406 - DEBUG - Translator: Main window created.
2025-05-31 10:30:39,406 - DEBUG - Translator: Attempting to setup keyboard listener...
2025-05-31 10:30:39,406 - DEBUG - 设置键盘监听器
2025-05-31 10:30:39,406 - DEBUG - Translator: Keyboard listener setup.
2025-05-31 10:30:39,406 - DEBUG - Translator: Attempting to start async loop thread...
2025-05-31 10:30:39,407 - DEBUG - Translator: Async loop thread started.
2025-05-31 10:30:39,407 - DEBUG - Translator: __init__ completed.
2025-05-31 10:30:39,465 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 10:30:39,508 - DEBUG - API加密工具已初始化
2025-05-31 10:30:39,510 - DEBUG - API密钥解密成功
2025-05-31 10:30:39,511 - DEBUG - API密钥解密成功
2025-05-31 10:30:39,511 - DEBUG - API密钥解密成功
2025-05-31 10:30:39,512 - DEBUG - API密钥解密成功
2025-05-31 10:30:39,512 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 10:30:39,516 - DEBUG - API密钥解密成功
2025-05-31 10:30:39,516 - DEBUG - API密钥解密成功
2025-05-31 10:30:39,517 - DEBUG - API密钥解密成功
2025-05-31 10:30:39,518 - DEBUG - API密钥解密成功
2025-05-31 10:30:39,519 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 10:30:41,063 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 10:30:41,166 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 10:30:41,167 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:30:41,168 - INFO - API服务正常
2025-05-31 10:30:41,279 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 10:30:41,384 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 10:30:41,385 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:31:00,459 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:31:00,474 - DEBUG - 配置验证成功
2025-05-31 10:31:00,475 - DEBUG - 配置保存并验证成功
2025-05-31 10:31:00,476 - INFO - 本地缓存已关闭
2025-05-31 10:31:01,336 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:31:01,351 - DEBUG - 配置验证成功
2025-05-31 10:31:01,352 - DEBUG - 配置保存并验证成功
2025-05-31 10:31:01,352 - INFO - 本地缓存已开启
