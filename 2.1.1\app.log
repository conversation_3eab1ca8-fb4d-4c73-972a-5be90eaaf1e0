2025-05-31 10:16:05,873 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 10:16:05,877 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml 不存在，正在自动生成默认配置...
2025-05-31 10:16:05,878 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:05,879 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:05,902 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:05,903 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:15,766 - INFO - API密钥验证成功
2025-05-31 10:16:15,795 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:15,796 - INFO - 已将加密的API密钥保存到配置文件
2025-05-31 10:16:15,797 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:15,812 - INFO - [INIT] 日志系统已初始化，调试模式：关闭
2025-05-31 10:16:15,874 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml 不存在，正在生成默认配置...
2025-05-31 10:16:15,876 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:15,932 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:16,060 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:16,062 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:16,063 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:16:16,193 - INFO - 语言模式配置已加载。
2025-05-31 10:16:16,194 - INFO - 语言模式配置已加载。
2025-05-31 10:16:16,194 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 10:16:16,195 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 10:16:16,207 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 10:16:16,314 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 10:16:16,917 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:16:16,918 - INFO - API服务正常
2025-05-31 10:16:17,133 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:16:21,230 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:16:21,252 - INFO - 已切换到翻译模式 2: 中文-韩文-敬语
