2025-05-31 10:05:59,317 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 10:05:59,321 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml 不存在，正在自动生成默认配置...
2025-05-31 10:05:59,323 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:05:59,324 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:05:59,350 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:05:59,350 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:06:06,017 - INFO - API密钥验证成功
2025-05-31 10:06:06,043 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:06:06,045 - INFO - 已将加密的API密钥保存到配置文件
2025-05-31 10:06:06,045 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:06:06,060 - INFO - [INIT] 日志系统已初始化，调试模式：关闭
2025-05-31 10:06:06,128 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml 不存在，正在生成默认配置...
2025-05-31 10:06:06,129 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:06:06,187 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:06:06,316 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:06:06,317 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:06:06,318 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 10:06:06,462 - INFO - 语言模式配置已加载。
2025-05-31 10:06:06,463 - INFO - 语言模式配置已加载。
2025-05-31 10:06:06,464 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 10:06:06,464 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 10:06:06,492 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 10:06:06,586 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 10:06:07,149 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:06:07,150 - INFO - API服务正常
2025-05-31 10:06:07,335 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 10:06:11,097 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:06:11,112 - INFO - 已切换到翻译模式 2: 中文-韩文-敬语
2025-05-31 10:06:13,383 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:06:13,397 - INFO - 已切换到翻译模式 13: 中文-意大利文-自然
2025-05-31 10:06:15,872 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 10:06:15,888 - INFO - 已切换到翻译模式 2: 中文-韩文-敬语
2025-05-31 10:06:31,765 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:06:31,767 - INFO - 【原文】
즐거운주말보네세요
2025-05-31 10:06:31,770 - INFO - 检测到原文语言: ko
2025-05-31 10:06:31,771 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:06:31,778 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:06:32,426 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:06:32,427 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 10:06:32,427 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 10:06:33,475 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-31 10:06:33,935 - INFO - 【翻译结果】
祝您周末愉快！
2025-05-31 10:06:33,936 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:06:33,938 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 10:06:50,037 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:06:50,039 - INFO - 【原文】
즐거운주말보네세요
2025-05-31 10:06:50,040 - INFO - 【缓存命中】键长度: 21, 值类型: str
2025-05-31 10:06:50,040 - INFO - 【语言检测】使用缓存结果: ko
2025-05-31 10:06:50,041 - INFO - 检测到原文语言: ko
2025-05-31 10:06:50,041 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:06:50,041 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-31 10:06:50,044 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 10:06:50,230 - INFO - 内存缓存命中
2025-05-31 10:06:50,231 - INFO - 【本地缓存命中】翻译结果从本地数据库获取
2025-05-31 10:06:50,232 - INFO - 【翻译结果】
祝您周末愉快！
2025-05-31 10:06:50,233 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 10:07:40,061 - INFO - 检测到三次空格，触发翻译
2025-05-31 10:07:40,062 - INFO - 【原文】
즐거운주말보네세요
2025-05-31 10:07:40,063 - INFO - 【缓存命中】键长度: 21, 值类型: str
2025-05-31 10:07:40,063 - INFO - 【语言检测】使用缓存结果: ko
2025-05-31 10:07:40,064 - INFO - 检测到原文语言: ko
2025-05-31 10:07:40,064 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 10:07:40,064 - INFO - 模式 2 当前上下文数量: 2（最大: 8）
2025-05-31 10:07:40,323 - INFO - 内存缓存命中
2025-05-31 10:07:40,325 - INFO - 【本地缓存命中】翻译结果从本地数据库获取
2025-05-31 10:07:40,325 - INFO - 【翻译结果】
祝您周末愉快！
2025-05-31 10:07:40,326 - INFO - 翻译完成，结果已替换输入框内容
