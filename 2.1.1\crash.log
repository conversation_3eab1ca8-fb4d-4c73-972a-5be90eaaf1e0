
================================================================================
程序崩溃报告
================================================================================
崩溃时间: 2025-05-31 10:22:27
崩溃类型: 手动记录: 测试手动崩溃记录
异常类型: ValueError
异常信息: 这是一个测试异常

系统信息:
操作系统: Windows 11
Python版本: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
程序路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\语言互译.py
是否打包: False
内存信息: 无法获取 (psutil未安装)
活跃线程数: 1
线程列表: MainThread
PATH: c:\Users\<USER>\.vscode\extensions\ms-python.python-2025.6.1-win32-x64\python_files\deactivate\powers...
PYTHONPATH: 未设置
TEMP: C:\Users\<USER>\AppData\Local\Temp
TMP: C:\Users\<USER>\AppData\Local\Temp
HOME: 未设置
USERPROFILE: C:\Users\<USER>\Users\admin\Desktop\代码\翻译软件代码\多语言互译\2.1.1\test_crash_logger.py", line 29, in test_crash_logger
    raise ValueError("这是一个测试异常")
ValueError: 这是一个测试异常

================================================================================


================================================================================
程序崩溃报告
================================================================================
崩溃时间: 2025-05-31 10:22:27
崩溃类型: 线程异常 (TestThread)
异常类型: RuntimeError
异常信息: 测试线程异常

系统信息:
操作系统: Windows 11
Python版本: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
程序路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\语言互译.py
是否打包: False
内存信息: 无法获取 (psutil未安装)
活跃线程数: 2
线程列表: MainThread, TestThread
PATH: c:\Users\<USER>\.vscode\extensions\ms-python.python-2025.6.1-win32-x64\python_files\deactivate\powers...
PYTHONPATH: 未设置
TEMP: C:\Users\<USER>\AppData\Local\Temp
TMP: C:\Users\<USER>\AppData\Local\Temp
HOME: 未设置
USERPROFILE: C:\Users\<USER>\Users\admin\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1041, in _bootstrap_inner
    self.run()
    ~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 992, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\test_crash_logger.py", line 46, in test_thread_function
    raise RuntimeError("测试线程异常")
RuntimeError: 测试线程异常

================================================================================

