"""
日志格式化模块 - 优化控制台日志输出显示效果

该模块提供了一个函数setup_log_formatter，用于优化控制台日志输出的显示效果。
主要功能包括：
1. 彩色日志级别显示
2. 模块名称显示
3. 关键词高亮
4. 消息长度限制
5. 打包环境兼容性
6. Windows控制台颜色支持

使用方法：
在主程序中导入该模块，并在配置加载后调用setup_log_formatter函数：

```python
from log_formatter import setup_log_formatter

# 加载配置
config = Config(**load_config())

# 应用日志格式化器
setup_log_formatter(config)
```

或者在main函数中添加：

```python
def main():
    # 加载配置
    config = Config(**load_config())

    # 应用日志格式化器
    setup_log_formatter(config)
    logger.info("已应用自定义日志格式，优化显示效果")

    # 其他代码...
```
"""

import logging
import os
import sys
import platform
from typing import Optional, Dict, Any

# 尝试导入colorama以支持Windows控制台颜色
try:
    import colorama
    from colorama import init as colorama_init, Fore, Back, Style
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False

def _setup_console_encoding():
    """设置控制台编码为UTF-8，确保中文字符正确显示"""
    try:
        # 检测是否在打包环境中运行
        is_packaged = getattr(sys, 'frozen', False)

        # 在Windows系统中设置控制台编码
        if platform.system() == 'Windows':
            try:
                # 设置控制台代码页为UTF-8
                import ctypes
                kernel32 = ctypes.windll.kernel32
                kernel32.SetConsoleCP(65001)  # UTF-8
                kernel32.SetConsoleOutputCP(65001)  # UTF-8
            except Exception:
                pass  # 忽略设置失败的情况

        # 设置标准输出编码
        if sys.stdout.encoding is None or sys.stdout.encoding.lower() != 'utf-8':
            try:
                from typing import cast
                from io import TextIOWrapper

                # Python 3.7+ 方法
                if isinstance(sys.stdout, TextIOWrapper) and hasattr(sys.stdout, 'reconfigure'):
                    stdout = cast(TextIOWrapper, sys.stdout)
                    stdout.reconfigure(encoding='utf-8', errors='replace')
                else:
                    raise AttributeError("reconfigure method not available")
            except (AttributeError, Exception):
                # 兼容旧版本Python或其他情况
                try:
                    import io
                    # 安全地获取line_buffering属性
                    line_buffering = getattr(sys.stdout, 'line_buffering', True)
                    if not isinstance(line_buffering, bool):
                        line_buffering = True

                    sys.stdout = io.TextIOWrapper(
                        sys.stdout.buffer,
                        encoding='utf-8',
                        errors='replace',
                        newline=None,
                        line_buffering=line_buffering
                    )
                except Exception:
                    pass  # 如果都失败了，保持原样

        # 同样处理stderr
        if sys.stderr.encoding is None or sys.stderr.encoding.lower() != 'utf-8':
            try:
                from typing import cast
                from io import TextIOWrapper

                if isinstance(sys.stderr, TextIOWrapper) and hasattr(sys.stderr, 'reconfigure'):
                    stderr = cast(TextIOWrapper, sys.stderr)
                    stderr.reconfigure(encoding='utf-8', errors='replace')
            except Exception:
                pass

    except Exception as e:
        # 如果编码设置失败，记录但不中断程序
        print(f"Warning: Failed to setup console encoding: {e}")

def _detect_color_support():
    """检测控制台是否支持颜色输出"""
    # 检测是否在打包环境中运行
    is_packaged = getattr(sys, 'frozen', False)

    # 在打包环境中，强制启用颜色支持（如果有colorama）
    if is_packaged and COLORAMA_AVAILABLE:
        return True

    # 检查环境变量
    if os.environ.get('FORCE_COLOR', '').lower() in ('1', 'true', 'yes'):
        return True

    if os.environ.get('NO_COLOR', '').lower() in ('1', 'true', 'yes'):
        return False

    # 检查是否为交互式终端
    try:
        if hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            return True
    except Exception:
        pass

    # Windows系统特殊处理
    if platform.system() == 'Windows':
        # 在Windows中，如果有colorama就支持颜色
        if COLORAMA_AVAILABLE:
            return True
        # 检查Windows Terminal或其他支持ANSI的终端
        if 'WT_SESSION' in os.environ or 'TERM' in os.environ:
            return True

    # 其他Unix系统检查TERM环境变量
    term = os.environ.get('TERM', '')
    if term and term != 'dumb':
        return True

    return False

def setup_log_formatter(config: Optional[Any] = None):
    """设置日志格式化器，优化日志显示效果

    Args:
        config: 配置对象，包含log_display配置项
    """
    # 首先设置控制台编码
    _setup_console_encoding()

    # 初始化colorama（如果可用）
    if COLORAMA_AVAILABLE:
        try:
            colorama_init(autoreset=True, convert=True, strip=False)
        except Exception:
            pass

    # 如果没有提供配置，使用默认配置
    if config is None or not hasattr(config, 'log_display'):
        log_display = {
            "max_message_length": 120,
            "show_module": True,
            "level_colors": {
                "DEBUG": "\033[36m",    # 青色
                "INFO": "\033[32m",     # 绿色
                "WARNING": "\033[33m",   # 黄色
                "ERROR": "\033[31m",     # 红色
                "CRITICAL": "\033[31;1m" # 亮红色
            },
            "time_format": "%H:%M:%S",
            "key_highlights": ["缓存", "API", "翻译", "错误", "检测", "网络", "配置"]
        }
    else:
        log_display = config.log_display

    # 检测是否支持颜色输出
    color_supported = _detect_color_support()

    # 自定义格式化器
    class HighlightFormatter(logging.Formatter):
        def format(self, record):
            try:
                # 保存原始消息
                original_msg = record.msg

                # 处理消息长度
                if isinstance(record.msg, str) and len(record.msg) > log_display["max_message_length"]:
                    record.msg = record.msg[:log_display["max_message_length"]] + "..."

                # 格式化消息
                message = super().format(record)

                # 恢复原始消息，避免影响日志文件
                record.msg = original_msg

                # 确保消息是字符串类型
                if not isinstance(message, str):
                    message = str(message)

                # 如果支持颜色，添加关键词高亮
                if color_supported:
                    for word in log_display["key_highlights"]:
                        if word in message:
                            # 使用更安全的颜色代码
                            if COLORAMA_AVAILABLE:
                                # 使用colorama的颜色代码
                                highlighted = f"{Style.BRIGHT}{Fore.YELLOW}{word}{Style.RESET_ALL}"
                            else:
                                # 使用ANSI颜色代码
                                highlighted = f"\033[1;33m{word}\033[0m"
                            message = message.replace(word, highlighted)

                # 确保消息以正确的编码返回
                return message

            except Exception as e:
                # 如果格式化失败，返回基本的消息
                try:
                    return f"[LOG ERROR] {record.getMessage()}"
                except:
                    return f"[LOG ERROR] Failed to format log message: {e}"

    # 获取根日志记录器
    root_logger = logging.getLogger()

    # 清除已有控制台处理器，保留文件处理器
    for handler in root_logger.handlers[:]:
        if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
            root_logger.removeHandler(handler)

    # 创建控制台处理器，确保使用正确的编码
    try:
        console_handler = logging.StreamHandler(sys.stdout)
        # 确保处理器使用UTF-8编码
        if hasattr(console_handler.stream, 'encoding'):
            if console_handler.stream.encoding != 'utf-8':
                try:
                    # 安全的类型检查和重新配置
                    from typing import cast
                    from io import TextIOWrapper

                    if isinstance(console_handler.stream, TextIOWrapper) and hasattr(console_handler.stream, 'reconfigure'):
                        stream = cast(TextIOWrapper, console_handler.stream)
                        stream.reconfigure(encoding='utf-8', errors='replace')
                except Exception:
                    pass
    except Exception:
        console_handler = logging.StreamHandler()

    # 根据是否支持颜色决定格式
    if color_supported:
        # 带颜色的格式
        if COLORAMA_AVAILABLE:
            # 使用colorama兼容的格式
            formatter = HighlightFormatter(
                f"%(asctime)s "
                f"%(levelcolor)s%(levelname)-8s{Style.RESET_ALL} "
                f"%(modinfo)s "
                f"%(message)s"
            )
        else:
            # 使用ANSI颜色代码
            formatter = HighlightFormatter(
                f"{log_display['level_colors'].get('INFO', '')}"
                f"%(asctime)s\033[0m "
                f"%(levelcolor)s%(levelname)-8s\033[0m "
                f"{log_display['level_colors'].get('INFO', '')}"
                f"%(modinfo)s\033[0m "
                f"%(message)s"
            )
    else:
        # 不带颜色的格式
        formatter = HighlightFormatter(
            f"%(asctime)s "
            f"%(levelname)-8s "
            f"%(modinfo)s "
            f"%(message)s"
        )

    # 设置日期格式
    formatter.datefmt = log_display["time_format"]

    # 注入自定义属性
    old_factory = logging.getLogRecordFactory()
    def record_factory(*args, **kwargs):
        try:
            record = old_factory(*args, **kwargs)

            # 添加模块信息
            if log_display["show_module"]:
                module = getattr(record, 'module', 'unknown')
                # 如果模块名过长，截断
                if len(module) > 12:
                    module = module[:10] + ".."
                record.modinfo = f"[{module}]"
            else:
                record.modinfo = ""

            # 添加日志级别颜色
            if color_supported:
                if COLORAMA_AVAILABLE:
                    # 使用colorama的颜色映射
                    color_map = {
                        'DEBUG': Fore.CYAN,
                        'INFO': Fore.GREEN,
                        'WARNING': Fore.YELLOW,
                        'ERROR': Fore.RED,
                        'CRITICAL': Fore.MAGENTA + Style.BRIGHT
                    }
                    record.levelcolor = color_map.get(record.levelname, Fore.WHITE)
                else:
                    # 使用ANSI颜色代码
                    record.levelcolor = log_display["level_colors"].get(record.levelname, "\033[0m")
            else:
                record.levelcolor = ""

            return record
        except Exception:
            # 如果记录工厂失败，返回基本记录
            record = old_factory(*args, **kwargs)
            record.modinfo = ""
            record.levelcolor = ""
            return record

    logging.setLogRecordFactory(record_factory)

    # 应用格式化器
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 保持原有日志级别
    if root_logger.level == 0:  # 未设置级别
        root_logger.setLevel(logging.INFO)

    # 输出初始化信息
    try:
        if color_supported:
            if COLORAMA_AVAILABLE:
                init_msg = f"{Fore.GREEN}[LOG] 日志格式化器已启用 (颜色支持: colorama){Style.RESET_ALL}"
            else:
                init_msg = f"\033[32m[LOG] 日志格式化器已启用 (颜色支持: ANSI)\033[0m"
        else:
            init_msg = "[LOG] 日志格式化器已启用 (无颜色支持)"

        # 使用print而不是logger避免循环
        print(init_msg)
    except Exception:
        print("[LOG] 日志格式化器已启用")

if __name__ == "__main__":
    # 简单测试
    print("开始测试日志格式化器...")

    # 测试编码设置
    print("测试控制台编码设置...")
    _setup_console_encoding()

    # 测试颜色支持检测
    color_support = _detect_color_support()
    print(f"颜色支持检测结果: {color_support}")
    print(f"Colorama可用性: {COLORAMA_AVAILABLE}")
    print(f"系统平台: {platform.system()}")
    print(f"是否打包环境: {getattr(sys, 'frozen', False)}")

    # 设置基本日志配置
    logging.basicConfig(level=logging.DEBUG, handlers=[])
    logger = logging.getLogger()

    # 应用格式化器
    setup_log_formatter()

    # 测试中文字符
    logger.info("测试中文字符显示：你好世界！")

    # 测试不同级别的日志
    logger.debug("这是一条调试日志")
    logger.info("这是一条信息日志，包含关键词：缓存")
    logger.warning("这是一条警告日志，包含关键词：网络")
    logger.error("这是一条错误日志，包含关键词：API")
    logger.critical("这是一条严重错误日志，包含关键词：翻译")

    # 测试长消息
    logger.info("这是一条非常长的日志消息" + "."*200 + "结束")

    # 测试特殊字符
    logger.info("测试特殊字符：©®™€£¥§¶†‡•…‰‹›""''–—")

    print("日志格式化器测试完成！")